<script setup>
import { computed, ref } from 'vue';
import { getCashierPayParams } from '@/api/order';
import { useUserStore } from '@/store/user';

const props = defineProps({
  order: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['refresh']);

// 用户store
const userStore = useUserStore();

// The action text depends on the order status
const actionText = computed(() => {
  switch (props.order.status) {
    case '0':
      return '去付款';
    case '20':
      return '确认收货';
    default:
      return null; // No button for other statuses
  }
});

// 处理按钮点击
const handleActionClick = async () => {
  if (props.order.status === '0') {
    // 去付款
    await handlePayment();
  } else if (props.order.status === '20') {
    // 确认收货
    handleConfirmReceipt();
  }
};

// 处理支付
const handlePayment = async () => {
  // 显示全局loading
  uni.showLoading({
    title: '支付中...',
    mask: true
  });

  try {
    // 获取支付参数
    const paymentParams = await getCashierPayParams(props.order.orderSn);

    // 拉起支付
    await uni.requestPayment({
      ...paymentParams.data,
      success: async () => {
        // 隐藏loading
        uni.hideLoading();

        uni.showToast({
          title: '支付成功',
          icon: 'success'
        });

        // 刷新用户信息（更新统计数据）
        try {
          await userStore.refreshUserInfo();
        } catch (error) {
          console.error('刷新用户信息失败:', error);
        }

        // 通知父组件刷新订单列表
        emit('refresh');
      },
      fail: (err) => {
        // 隐藏loading
        uni.hideLoading();

        console.error('支付失败:', err);
        // 用户主动取消支付时不显示错误提示
        if (err.errMsg && !err.errMsg.includes('cancel')) {
          uni.showToast({
            title: '支付失败',
            icon: 'none'
          });
        }
      }
    });
  } catch (error) {
    // 隐藏loading
    uni.hideLoading();

    console.error('获取支付参数失败:', error);
    uni.showToast({
      title: '支付失败',
      icon: 'none'
    });
  }
};

// 处理确认收货
const handleConfirmReceipt = () => {
  uni.showModal({
    title: '确认收货',
    content: '确定已收到商品吗？',
    success: (res) => {
      if (res.confirm) {
        // TODO: 调用确认收货API
        uni.showToast({
          title: '确认收货成功',
          icon: 'success'
        });
        // 通知父组件刷新订单列表
        emit('refresh');
      }
    }
  });
};

// 复制物流单号
const copyLogisticsNo = () => {
  if (!props.order.logisticsNo) {
    uni.showToast({
      title: '暂无物流单号',
      icon: 'none'
    });
    return;
  }

  uni.setClipboardData({
    data: props.order.logisticsNo,
    success: () => {
      uni.showToast({
        title: '复制成功',
        icon: 'success'
      });
    },
    fail: () => {
      uni.showToast({
        title: '复制失败',
        icon: 'none'
      });
    }
  });
};

// 处理图片预览
const handleImagePreview = () => {
  const imageUrls = props.order.treeDetail.imageUrls;
  if (imageUrls && imageUrls.length > 0) {
    uni.previewImage({
      urls: imageUrls,
      current: 0, // 从第一张图片开始显示
      fail: (err) => {
        console.error('图片预览失败:', err);
        uni.showToast({
          title: '图片预览失败',
          icon: 'none'
        });
      }
    });
  } else {
    uni.showToast({
      title: '暂无图片',
      icon: 'none'
    });
  }
};
</script>

<template>
  <view class="order-item-card">
    <view class="card-body">
      <view class="product-image-container" @click="handleImagePreview">
        <image
          class="product-image"
          :src="order.treeDetail.imageUrls[0]"
          mode="aspectFill"
        />
      </view>
      <text class="product-main-title">{{ order.treeDetail.name }}</text>
      <text class="product-sub-title">{{ order.treeDetail.description }}</text>

      <!-- 待收货订单显示物流信息 -->
      <view v-if="order.status === '20'" class="logistics-info">
        <view class="logistics-item">
          <text class="logistics-label">物流公司：</text>
          <text class="logistics-value">{{ order.logisticsName || '-' }}</text>
        </view>
        <view class="logistics-item">
          <text class="logistics-label">物流单号：</text>
          <text class="logistics-value">{{ order.logisticsNo || '-' }}</text>
          <view v-if="order.logisticsNo" class="copy-button" @click="copyLogisticsNo">
            <text class="copy-text">复制</text>
          </view>
        </view>
      </view>

      <view class="actions-container">
        <view class="price-info">
          <text class="price">¥ {{ order.totalPrice.toFixed(2) }}</text>
          <text class="quantity">数量: {{ order.fruitNum }}</text>
        </view>
        <view v-if="actionText" class="action-button" @click="handleActionClick">
          <text class="button-text">{{ actionText }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.order-item-card {
  width: 686rpx;
  margin-left: auto;
  margin-right: auto;
}

.card-body {
  position: relative;
  width: 100%;
  min-height: 615rpx;
  border-radius: 16rpx;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  isolation: isolate;
}

.product-image-container {
  position: relative;
  width: 100%;
  height: 378rpx;
  overflow: hidden;
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
  cursor: pointer;

  &:active {
    opacity: 0.8;
  }
}

.product-image {
  width: 100%;
  height: 100%;
}

.product-main-title {
  position: relative;
  font-size: 32rpx;
  font-weight: 500;
  line-height: 38rpx;
  color: #1a1a1a;
  white-space: normal;
  word-wrap: break-word;
  margin: 24rpx 32rpx 12rpx 32rpx;
  z-index: 1;
}

.product-sub-title {
  position: relative;
  width: 622rpx;
  font-size: 30rpx;
  font-weight: 400;
  line-height: 35rpx;
  color: #999999;
  white-space: normal;
  word-wrap: break-word;
  z-index: 2;
  margin: 0 32rpx 20rpx 32rpx;
}

.logistics-info {
  margin: 0 32rpx 20rpx 32rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  z-index: 2;
  width: calc(100% - 64rpx);
  box-sizing: border-box;
}

.logistics-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 8rpx;
}

.logistics-item:last-child {
  margin-bottom: 0;
}

.logistics-label {
  font-size: 28rpx;
  font-weight: 400;
  color: #666666;
  white-space: nowrap;
  min-width: 140rpx;
  flex-shrink: 0;
}

.logistics-value {
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
  flex: 1;
  word-break: break-all;
  line-height: 1.4;
  margin-right: 8rpx;
  min-width: 0;
}

.copy-button {
  padding: 8rpx 16rpx;
  background-color: #dd3c29;
  border-radius: 8rpx;
  flex-shrink: 0;
}

.copy-text {
  font-size: 24rpx;
  font-weight: 400;
  color: #ffffff;
  line-height: 1;
}

.actions-container {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 64rpx;
  z-index: 3;
  margin-top: auto;
  margin-bottom: 20rpx;
  padding: 0 32rpx;
  box-sizing: border-box;
}

.price-info {
  display: flex;
  flex-direction: row;
  align-items: baseline;
}

.price {
  font-size: 36rpx;
  font-weight: 700;
  line-height: 42rpx;
  color: #ea0000;
  white-space: pre;
}

.quantity {
  margin-left: 20rpx;
  font-size: 28rpx;
  font-weight: 400;
  line-height: 33rpx;
  color: #999999;
  white-space: pre;
}

.action-button {
  width: 166rpx;
  height: 64rpx;
  border-radius: 32rpx;
  background-color: #dd3c29;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.action-button.loading {
  background-color: #999999;
  pointer-events: none;
}

.button-text {
  font-size: 30rpx;
  font-weight: 400;
  line-height: 35rpx;
  color: #ffffff;
  white-space: pre;
}
</style>
